---
type: "always_apply"
---

# Tests

## Checking for row insertion or update

When checking if a row is inserted or updated, always verify its new value directly. Do not rely on error checks for this purpose.

```typescript
expect(profileUpdate.data?.slug).toBe(`test-user-updated`);
```

## Do not trust null or defined checks

Do not trust null or defined checks when testing for row existence. Always verify the row's value directly by making an extra select request.

### Do

```typescript
const { data, error } = await customer1.client
  .schema("app_account")
  .from("social_link")
  .select("*");

expect(data?.user_id).toBe(customer1.data.id);
// or
expect(data).toHaveLength(0);
// or
expect(data?.some((link) => link.user_id === customer.data?.id)).toBe(true);
```

### Don't

```typescript
expect(error).toBeNull();
expect(data).toBeDefined();
```

## Cleaning up after the tests

Ensure proper test cleanup by keeping track of created object IDs and removing them in the afterAll hook, rather than performing global deletions.

### Do

```typescript
const createdIds: string[] = [];

afterAll(async () => {
  await serviceClient
    .schema("app_chat")
    .from("conversation")
    .delete()
    .in("id", createdIds);
});
```

### Don't

```typescript
afterAll(async () => {
  await serviceClient
    .schema("app_account")
    .from("social_link")
    .delete()
    .neq("id", "********-0000-0000-0000-************");
});
```

Alternatively, consider creating a mock function if you anticipate needing it for future test cases.

### Examples

- `mocks/app_account.social_link.ts`
- `mocks/app_chat.conversation.ts`
- `mocks/app_chat.message.ts`
- `mocks/app_chat.conversation_preference.ts`
- `mocks/app_provider.service.ts`
- `mocks/app_provider.order.ts`
- `mocks/app_provider.review.ts`
- `mocks/app_provider.application.ts`
- `mocks/app_provider.user_favorite.ts`
- `mocks/app_catalog.activity.ts`

This mock functions handles both setup and cleaning in test files.

```ts
const customer = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();
const service = mockService(provider);
```

## Run these commands after test file changes

```bash
pnpm typecheck:supabase-tests
```