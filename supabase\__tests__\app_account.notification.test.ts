import { afterAll, beforeAll, describe, expect, test } from "vitest";
import {
  mockAdmin,
  mockCustomer,
  mockProvider,
  MockUser
} from "./mocks/auth.user";
import { mockService } from "./mocks/app_provider.service";
import { mockOrder } from "./mocks/app_provider.order";
import { serviceClient } from "./utils/client";
import { RealtimeChannel } from "@supabase/supabase-js";
import {
  validateNotification,
  validateNotificationData,
  type NotificationType
} from "shared/schemas/notification.schema";

type NotificationChannelParams = {
  user: MockUser;
  receivedNotifications?: any[];
};

// Helper function to validate and find notifications
function findValidatedNotification<T extends NotificationType>(
  notifications: any[],
  type: T,
  predicate?: (data: any) => boolean
) {
  const notification = notifications.find(n => n.type === type);
  if (!notification) return null;

  // Validate the notification structure
  const validatedNotification = validateNotification(notification);

  // Validate the notification data
  const validatedData = validateNotificationData(type, validatedNotification.data);

  // Check predicate if provided
  if (predicate && !predicate(validatedData)) return null;

  return { notification: validatedNotification, data: validatedData };
}

async function createNotificationChannel({
  user,
  receivedNotifications = []
}: NotificationChannelParams) {
  if (!user.client || !user.data)
    throw new Error("User client or data is undefined");

  await user.client.realtime.setAuth();

  return await new Promise<RealtimeChannel>((resolve, reject) => {
    if (!user.client || !user.data)
      throw new Error("User client or data is undefined");

    const channel = user.client
      .channel(`notification:${user.data.id}`, {
        config: { private: true, broadcast: { ack: true } }
      })
      .on("broadcast", { event: "INSERT" }, ({ payload }) => {
        receivedNotifications.push(payload);
      })
      .subscribe(async (status) => {
        console.log(`Notification channel status: ${status}`);
        if (status === "SUBSCRIBED") resolve(channel);
        if (status === "CHANNEL_ERROR") reject();
      });
  });
}

describe("Notification System", () => {
  const admin = mockAdmin();
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];
  const createdUserIds: string[] = [];

  describe("RLS Policies", () => {
    test("users can only see their own notifications", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");
      if (!provider.data) throw new Error("Provider not defined");

      // Create a notification for the customer
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.notification",
          p_recipient_id: customer.data.id,
          p_title_key: "test.title",
          p_message_key: "test.message",
          p_data: { test: "data" }
        });

      if (notification?.id) createdNotificationIds.push(notification.id);

      // Customer should see their notification
      const { data: customerNotifications } = await customer.client
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("type", "test.notification");

      expect(
        customerNotifications?.some((n) => n.recipient_id === customer.data?.id)
      ).toBe(true);

      // Provider should not see customer's notification
      if (!provider.client) throw new Error("Provider client not defined");
      const { data: providerNotifications } = await provider.client
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id);

      expect(providerNotifications).toHaveLength(0);
    });

    test("users can delete their own notifications", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a notification for the customer
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.delete",
          p_recipient_id: customer.data.id,
          p_title_key: "test.title",
          p_message_key: "test.message",
          p_data: { test: "data" }
        });

      if (!notification?.id) throw Error("Failed to create notification");

      createdNotificationIds.push(notification.id);

      // Customer should be able to delete their notification
      await customer.client
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("id", notification.id);

      // Verify notification was deleted
      const { data: deletedNotification } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("id", notification.id);

      expect(deletedNotification).toHaveLength(0);
    });
  });

  describe("Realtime Broadcasting", () => {
    const receivedNotifications: any[] = [];

    beforeAll(async () => {
      if (!customer.data) throw new Error("Customer not defined");

      await createNotificationChannel({
        user: customer,
        receivedNotifications
      });

      // Wait a bit for the channel to be ready
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }, 30000);

    test("notifications are broadcast in realtime", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      const initialCount = receivedNotifications.length;

      // Create a notification
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.realtime",
          p_recipient_id: customer.data.id,
          p_title_key: "test.realtime.title",
          p_message_key: "test.realtime.message",
          p_data: { realtime: true }
        });

      if (notification?.id) createdNotificationIds.push(notification.id);

      // Wait for the broadcast
      await new Promise((resolve) => setTimeout(resolve, 500));

      expect(receivedNotifications.length).toBeGreaterThan(initialCount);

      const lastNotification =
        receivedNotifications[receivedNotifications.length - 1];
      expect(lastNotification.type).toBe("test.realtime");
      expect(lastNotification.data.realtime).toBe(true);
    });
  });

  describe("Account Management Notifications", () => {
    test("welcome notification is sent on user signup", async () => {
      // Create a new user
      const newUser = await serviceClient.auth.admin.createUser({
        email: "<EMAIL>",
        password: "password123",
        email_confirm: true
      });

      expect(newUser.data.user).toBeDefined();

      if (newUser.data.user) {
        createdUserIds.push(newUser.data.user.id);

        // Check if welcome notification was created
        const { data: notifications } = await serviceClient
          .schema("app_account")
          .from("notification")
          .select("*")
          .eq("recipient_id", newUser.data.user.id)
          .eq("type", "account.welcome");

        expect(notifications).toHaveLength(1);
        expect(notifications?.[0].title_key).toBe(
          "notifications.account.welcome.title"
        );

        if (notifications?.[0].id)
          createdNotificationIds.push(notifications[0].id);
      }
    });

    test("KYC status change notification is sent", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create KYC record
      await customer.client.schema("app_account").from("kyc").insert({
        user_id: customer.data.id,
        full_name: "Test User",
        status: "pending"
      });

      // Admin updates KYC status
      await serviceClient
        .schema("app_account")
        .from("kyc")
        .update({ status: "approved" })
        .eq("user_id", customer.data.id);

      // Check if notification was created and validate its structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "account.kyc.status_change");

      expect(notifications).toBeDefined();
      expect(notifications!.length).toBeGreaterThan(0);

      const validatedNotification = findValidatedNotification(
        notifications!,
        "account.kyc.status_change",
        (data) => data.new_status === "approved"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.new_status).toBe("approved");
      expect(validatedNotification!.data.old_status).toBe("pending");

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("ban notification is sent when user is banned", async () => {
      if (!customer.data || !admin.data)
        throw new Error("Customer or admin not defined");

      // Admin bans the customer
      await serviceClient.schema("app_account").from("banned_user").insert({
        user_id: customer.data.id,
        banned_by: admin.data.id,
        reason: "Test ban"
      });

      // Check if notification was created and validate its structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "account.banned");

      expect(notifications).toBeDefined();
      expect(notifications!.length).toBeGreaterThan(0);

      const validatedNotification = findValidatedNotification(
        notifications!,
        "account.banned",
        (data) => data.reason === "Test ban"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.reason).toBe("Test ban");
      expect(validatedNotification!.data.banned_by).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }

      // Cleanup - unban user
      await serviceClient
        .schema("app_account")
        .from("banned_user")
        .delete()
        .eq("user_id", customer.data.id);
    });
  });

  describe("Role Change Notifications", () => {
    test("role assignment notification is sent", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // Clear existing notifications first
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.assigned");

      // Assign provider role
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: customer.data.id,
        v_role_name: "provider"
      });

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "access.role.assigned")
        .order("created_at", { ascending: false });

      expect(notifications).toBeDefined();
      expect(notifications!.length).toBeGreaterThan(0);

      const validatedNotification = findValidatedNotification(
        notifications!,
        "access.role.assigned",
        (data) => data.role_name === "provider"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.role_name).toBe("provider");
      expect(validatedNotification!.data.assigned_at).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }

      // Cleanup - remove role
      await serviceClient
        .schema("app_access")
        .from("user_role")
        .delete()
        .eq("user_id", customer.data.id)
        .eq("role_id", 3); // provider role id
    });
  });

  describe("Provider and Order Notifications", () => {
    test("new order notification is sent to provider", async () => {
      if (!customer.client || !customer.data || !provider.data) {
        throw new Error("Customer or provider not defined");
      }
      if (!customer.data.id) throw new Error("Customer ID not defined");

      // Create test activity and pricing data
      const { data: activity, error: activityError } = await serviceClient
        .schema("app_catalog")
        .from("activity")
        .insert({
          name: { en: "Test Activity" },
          description: { en: "Test Activity Description" }
        })
        .select("id")
        .single();

      const { data: pricing, error: pricingError } = await serviceClient
        .schema("app_catalog")
        .from("pricing")
        .insert({
          name: { en: "Test Pricing" },
          description: { en: "Test Pricing Description" }
        })
        .select("id")
        .single();

      if (activityError) {
        console.log("Activity creation error:", activityError);
      }
      if (pricingError) {
        console.log("Pricing creation error:", pricingError);
      }

      if (!activity?.id || !pricing?.id) {
        throw new Error(`Failed to create test data - Activity: ${activity?.id}, Pricing: ${pricing?.id}`);
      }

      // Create a provider activity that references the catalog activity
      const { data: providerActivity, error: providerActivityError } = await serviceClient
        .schema("app_provider")
        .from("activity")
        .insert({
          user_id: provider.data.id,
          activity_id: activity.id
        })
        .select()
        .single();

      if (providerActivityError) {
        console.log("Provider activity creation error:", providerActivityError);
      }

      if (!providerActivity?.id) {
        throw new Error(`Failed to create provider activity - Error: ${providerActivityError?.message}`);
      }

      // Create a service for the provider
      const { data: service, error: serviceError } = await serviceClient
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: provider.data.id,
          activity_id: providerActivity.id,
          name: { en: "Test Service" },
          description: { en: "Test Description" },
          soda_amount: 100,
          pricing_id: pricing.id
        })
        .select()
        .single();

      if (serviceError) {
        console.log("Service creation error:", serviceError);
      }

      if (!service?.id) throw new Error(`Service not defined - Error: ${serviceError?.message}`);

      // Make sure provider is approved
      await serviceClient
        .schema("app_provider")
        .from("approved_user")
        .insert({
          user_id: provider.data.id
        });

      // Customer creates an order using service client to bypass RLS
      const { data: order, error: orderError } = await serviceClient
        .schema("app_provider")
        .from("order")
        .insert({
          sender_id: customer.data.id,
          receiver_id: provider.data.id,
          service_id: service.id,
          soda_amount: 100,
          order_status: "pending"
        })
        .select()
        .single();

      if (orderError) {
        console.log("Order creation error:", orderError);
      }

      if (!order) {
        throw new Error("Order creation failed");
      }

      // Check if notification was created for provider and validate structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "provider.order.new");

      expect(notifications).toBeDefined();
      expect(notifications!.length).toBeGreaterThan(0);

      const validatedNotification = findValidatedNotification(
        notifications!,
        "provider.order.new",
        (data) => data.order_id === order.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.order_id).toBe(order.id);
      expect(validatedNotification!.data.customer_username).toBeDefined();
      expect(validatedNotification!.data.service_name).toBeDefined();
      expect(validatedNotification!.data.amount).toBeGreaterThan(0);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }

      // Cleanup
      await serviceClient
        .schema("app_provider")
        .from("order")
        .delete()
        .eq("id", order.id);
      await serviceClient
        .schema("app_provider")
        .from("service")
        .delete()
        .eq("id", service.id);
      await serviceClient
        .schema("app_provider")
        .from("activity")
        .delete()
        .eq("id", providerActivity.id);
      await serviceClient
        .schema("app_provider")
        .from("approved_user")
        .delete()
        .eq("user_id", provider.data.id);
      await serviceClient
        .schema("app_catalog")
        .from("activity")
        .delete()
        .eq("id", activity.id);
      await serviceClient
        .schema("app_catalog")
        .from("pricing")
        .delete()
        .eq("id", pricing.id);
    });
  });

  describe("Chat Notifications", () => {
    test("new message notification is sent to conversation members", async () => {
      if (
        !customer.client ||
        !customer.data ||
        !provider.client ||
        !provider.data
      ) {
        throw new Error("Customer or provider not defined");
      }

      // Create a conversation using service client (has more permissions)
      const { data: conversation, error: convError } = await serviceClient
        .schema("app_chat")
        .from("conversation")
        .insert({})
        .select()
        .single();

      if (convError) {
        console.log("Conversation creation error:", convError);
        throw convError;
      }

      // Add members to conversation
      await serviceClient
        .schema("app_chat")
        .from("member")
        .insert([
          { conversation_id: conversation.id, user_id: customer.data.id },
          { conversation_id: conversation.id, user_id: provider.data.id }
        ]);

      // Customer sends a message using service client to avoid RLS issues
      await serviceClient.schema("app_chat").from("message").insert({
        conversation_id: conversation.id,
        sender_id: customer.data.id,
        content: "Hello provider!"
      });

      // Check if notification was created for provider
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "chat.message.new");

      expect(notifications).toBeDefined();
      expect(notifications!.length).toBeGreaterThan(0);

      const validatedNotification = findValidatedNotification(
        notifications!,
        "chat.message.new",
        (data) => data.conversation_id === conversation.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.conversation_id).toBe(conversation.id);
      expect(validatedNotification!.data.sender_username).toBeDefined();
      expect(validatedNotification!.data.content_preview).toBe('"Hello provider!"');
      expect(validatedNotification!.data.message_id).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }

      // Cleanup
      await serviceClient
        .schema("app_chat")
        .from("conversation")
        .delete()
        .eq("id", conversation.id);
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created users
    if (createdUserIds.length > 0) {
      for (const userId of createdUserIds) {
        await serviceClient.auth.admin.deleteUser(userId);
      }
    }
  });
});
