import { describe, test, expect, beforeAll, afterAll } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";

const customer = mockCustomer();
const admin = mockAdmin();
const provider = mockProvider();

describe("Banned User Functionality", () => {
  beforeAll(async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
  });

  afterAll(async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    // Clean up: remove any banned users
    await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", customer.data.id);
  });

  test("Admin can ban a user and roles are seized", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    // First, assign provider role to customer for testing
    const assignRole = await admin.client
      .schema("app_access")
      .rpc("assign_role_to_user", {
        v_user_id: customer.data.id,
        v_role_name: "provider"
      });
    expect(assignRole.data).toBe(true);

    // Verify user has both customer and provider roles
    const userRolesBefore = await admin.client
      .schema("app_access")
      .from("user_role")
      .select(
        `
        role_id,
        role:role_id(name)
      `
      )
      .eq("user_id", customer.data.id);

    expect(userRolesBefore.error).toBeNull();
    expect(userRolesBefore.data?.length).toBeGreaterThan(0);

    const roleNamesBefore =
      userRolesBefore.data?.map((ur: any) => ur.role.name) || [];
    expect(roleNamesBefore).toContain("customer");
    expect(roleNamesBefore).toContain("provider");

    // Ban the user
    const banUser = await admin.client
      .schema("app_account")
      .from("banned_user")
      .insert({
        user_id: customer.data.id,
        banned_by: admin.data?.id,
        reason: "Test ban"
      });

    expect(banUser.error).toBeNull();

    // Verify user has no roles after ban
    const userRolesAfter = await admin.client
      .schema("app_access")
      .from("user_role")
      .select("*")
      .eq("user_id", customer.data.id);

    expect(userRolesAfter.error).toBeNull();
    expect(userRolesAfter.data?.length).toBe(0);

    // Verify banned_user record contains previous roles
    const bannedUserRecord = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(bannedUserRecord.error).toBeNull();
    expect(bannedUserRecord.data?.previous_roles).toContain("customer");
    expect(bannedUserRecord.data?.previous_roles).toContain("provider");
    expect(bannedUserRecord.data?.reason).toBe("Test ban");
    expect(bannedUserRecord.data?.banned_by).toBe(admin.data?.id);
  });

  test("Admin can unban a user and roles are restored", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    // Verify user is currently banned and has no roles
    const userRolesBefore = await admin.client
      .schema("app_access")
      .from("user_role")
      .select("*")
      .eq("user_id", customer.data.id);

    expect(userRolesBefore.error).toBeNull();
    expect(userRolesBefore.data?.length).toBe(0);

    // Get the banned user record to verify previous roles
    const bannedUserRecord = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(bannedUserRecord.error).toBeNull();
    const previousRoles = bannedUserRecord.data?.previous_roles || [];
    expect(previousRoles.length).toBeGreaterThan(0);

    // Unban the user by deleting the banned_user record
    const unbanUser = await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", customer.data.id);

    expect(unbanUser.error).toBeNull();

    // Verify user has their roles restored
    const userRolesAfter = await admin.client
      .schema("app_access")
      .from("user_role")
      .select(
        `
        role_id,
        role:role_id(name)
      `
      )
      .eq("user_id", customer.data.id);

    expect(userRolesAfter.error).toBeNull();
    expect(userRolesAfter.data?.length).toBeGreaterThan(0);

    const roleNamesAfter =
      userRolesAfter.data?.map((ur: any) => ur.role.name) || [];

    // Verify all previous roles are restored
    previousRoles.forEach((roleName) => {
      expect(roleNamesAfter).toContain(roleName);
    });

    // Verify banned_user record is deleted
    const bannedUserCheck = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*")
      .eq("user_id", customer.data.id);

    expect(bannedUserCheck.error).toBeNull();
    expect(bannedUserCheck.data?.length).toBe(0);
  });

  test("Normal user cannot ban other users", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    const banAttempt = await customer.client
      .schema("app_account")
      .from("banned_user")
      .insert({
        user_id: provider.data.id,
        reason: "Unauthorized ban attempt"
      });

    expect(banAttempt.error).not.toBeNull();
  });

  test("Normal user cannot unban users", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // First ban a user as admin
    const banUser = await admin.client
      .schema("app_account")
      .from("banned_user")
      .insert({
        user_id: provider.data.id,
        banned_by: admin.data?.id,
        reason: "Test ban for unban test"
      });

    expect(banUser.error).toBeNull();

    // Try to unban as normal user - should fail due to RLS
    const unbanAttempt = await customer.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", provider.data.id);

    // Check if the operation was actually blocked by verifying the record still exists
    const checkBannedUser = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*")
      .eq("user_id", provider.data.id);

    expect(checkBannedUser.data?.length).toBe(1); // User should still be banned

    // Clean up: unban as admin
    await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", provider.data.id);
  });

  test("Banning user with no roles stores empty array", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // First ensure provider is not banned
    await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", provider.data.id);

    // Get all current roles and remove them
    const currentRoles = await admin.client
      .schema("app_access")
      .from("user_role")
      .select(
        `
        role_id,
        role:role_id(name)
      `
      )
      .eq("user_id", provider.data.id);

    // Remove all roles from provider user
    if (currentRoles.data && currentRoles.data.length > 0) {
      for (const userRole of currentRoles.data) {
        await admin.client.schema("app_access").rpc("revoke_role_from_user", {
          v_user_id: provider.data.id,
          v_role_name: userRole.role.name
        });
      }
    }

    // Verify user has no roles
    const userRolesBefore = await admin.client
      .schema("app_access")
      .from("user_role")
      .select("*")
      .eq("user_id", provider.data.id);

    expect(userRolesBefore.data?.length).toBe(0);

    // Ban the user
    const banUser = await admin.client
      .schema("app_account")
      .from("banned_user")
      .insert({
        user_id: provider.data.id,
        banned_by: admin.data?.id,
        reason: "Test ban with no roles"
      });

    expect(banUser.error).toBeNull();

    // Verify banned_user record has empty previous_roles array
    const bannedUserRecord = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*")
      .eq("user_id", provider.data.id)
      .single();

    expect(bannedUserRecord.error).toBeNull();
    expect(bannedUserRecord.data?.previous_roles).toEqual([]);

    // Clean up: unban the user and restore customer role
    await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", provider.data.id);

    await admin.client.schema("app_access").rpc("assign_role_to_user", {
      v_user_id: provider.data.id,
      v_role_name: "customer"
    });
  });

  test("Admin can view banned users", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    // Ban a user first
    await admin.client.schema("app_account").from("banned_user").insert({
      user_id: customer.data.id,
      banned_by: admin.data?.id,
      reason: "Test ban for view test"
    });

    // Admin should be able to view banned users
    const viewBannedUsers = await admin.client
      .schema("app_account")
      .from("banned_user")
      .select("*");

    expect(viewBannedUsers.error).toBeNull();
    expect(viewBannedUsers.data?.length).toBeGreaterThan(0);

    // Clean up
    await admin.client
      .schema("app_account")
      .from("banned_user")
      .delete()
      .eq("user_id", customer.data.id);
  });

  test("Normal user cannot view banned users", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const viewAttempt = await customer.client
      .schema("app_account")
      .from("banned_user")
      .select("*");

    // RLS should block access, so either error or empty result
    if (viewAttempt.error) {
      expect(viewAttempt.error).not.toBeNull();
    } else {
      // If no error, should return empty array due to RLS
      expect(viewAttempt.data?.length).toBe(0);
    }
  });
});
