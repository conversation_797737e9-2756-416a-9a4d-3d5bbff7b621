{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/4294a_@supabase_auth-js_dist_module_51b8369e._.js", "server/edge/chunks/node_modules__pnpm_69d3902d._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_67351805.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwpAgcBgXqjbQWOpyrp+dSFW0SqENk1GPyl8AcBB52I=", "__NEXT_PREVIEW_MODE_ID": "aafb55f13fb4ea52fad9ca3335cc6ee9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a04e7252be0339912666e63b32fbb5effa5733f11dc333926ffe0639fcaf78fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f256c99372e07f9b3c9a62544b6a24c9989867decdb556320ac9264567045001"}}}, "instrumentation": null, "functions": {}}