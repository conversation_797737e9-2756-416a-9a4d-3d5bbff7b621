DROP POLICY if E<PERSON>ISTS "realtime_messages_select" ON "realtime"."messages"
;

CREATE POLICY "realtime_messages_select" ON "realtime"."messages" FOR
SELECT
  TO authenticated USING (
    (
      -- Chat messages: user must be a member of the conversation
      realtime.topic () LIKE 'chat:%'
      AND app_chat.is_member (
        REPLACE(
          realtime.topic (),
          'chat:',
          ''
        )::UUID,
        auth.uid ()
      )
      AND realtime.messages.extension IN ('broadcast', 'presence')
    )
    OR (
      -- Notification messages: user must be the recipient
      realtime.topic () LIKE 'notification:%'
      AND REPLACE(
        realtime.topic (),
        'notification:',
        ''
      )::UUID = auth.uid ()
      AND realtime.messages.extension IN ('broadcast', 'presence')
    )
    OR (
      -- Online presence: anyone can access
      realtime.topic () LIKE 'online:%'
      AND realtime.messages.extension IN ('presence')
    )
  )
;
